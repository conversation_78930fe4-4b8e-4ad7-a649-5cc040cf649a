export interface FilterValue {
  label: string;
  value: string;
  productVariantIds: string[];
  metadata?: {
    previewUrl?: string;
    parentId?: string | null;
    position?: number;
  };
}
export interface FilterGroup {
  label: string;
  isFacet: boolean;
  values: FilterValue[];
  metadata?: {
    type?: 'slider';
    min: number;
    max: number;
  };
}
export interface IFilter {
  limit?: number;
  skip?: number;
  order?: Array<Record<string, unknown> | string>;
  where?: Record<string, unknown>;
  fields?: Record<string, boolean>;
  include?: Array<Record<string, unknown> | string>;
}

export interface IFilterWithKeyword extends IFilter {
  keyword?: string;
  facetValueIds?: string[];
  collectionIds?: string[];
  priceRange?: number[];
  thresholdType?: 'price' | 'discount';
  thresholdValue?: number;
  thresholdOperator?: 'above' | 'below';
}
