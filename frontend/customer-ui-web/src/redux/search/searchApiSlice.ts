import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from '../../redux/apiSlice';
import {ProductVariant} from 'types/product';
import {FilterGroup, IFilterWithKeyword} from 'types/filter';

export const searchApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getSearchSuggestions: builder.query<ProductVariant[], string>({
      query: keyword => ({
        url: '/search/suggestions',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword,
        },
      }),
    }),
    getQuickFilter: builder.query<FilterGroup, string>({
      query: productVariantIds => ({
        url: '/search/quick-filter',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          productVariantIds,
        },
      }),
    }),

    search: builder.query<ProductVariant[], IFilterWithKeyword>({
      query: ({
        keyword,
        facetValueIds,
        collectionIds,
        priceRange,
        thresholdType,
        thresholdValue,
        thresholdOperator,
        ...filter
      }) => ({
        url: '/search',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword: keyword,
          facetValueIds,
          collectionIds,
          priceRange,
          thresholdType,
          thresholdValue,
          thresholdOperator,
          filter: JSON.stringify(filter),
        },
      }),
    }),
    filters: builder.query<
      FilterGroup[],
      {
        keyword?: string;
        facetValueIds?: string[];
        collectionIds?: string[];
        thresholdType?: 'price' | 'discount';
        thresholdValue?: number;
        thresholdOperator?: 'above' | 'below';
      }
    >({
      query: ({
        keyword,
        facetValueIds,
        collectionIds,
        thresholdType,
        thresholdValue,
        thresholdOperator,
      }) => ({
        url: '/search/filters',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword,
          facetValueIds,
          collectionIds,
          thresholdType,
          thresholdValue,
          thresholdOperator,
        },
      }),
    }),
  }),
});
export const {
  useGetSearchSuggestionsQuery,
  useLazyGetSearchSuggestionsQuery,
  useSearchQuery,
  useFiltersQuery,
  useLazySearchQuery,
  useGetQuickFilterQuery,
} = searchApiSlice;
