import {Box, Stack, Typography} from '@mui/material';
import {PageSection, SectionItem} from 'types/page-section';

const CARD_STYLE = {
  width: 140, // decreased width
  height: 260, // increased height
  borderRadius: 2,
  cursor: 'pointer',
  overflow: 'hidden',
  boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
  textAlign: 'center',
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
  },
};

const PRICE_TITLE_STYLE = {
  backgroundColor: '#C8D3D5',
  padding: '8px',
  minHeight: '120px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
};

const DISCOUNT_TITLE_STYLE = {
  backgroundColor: '#C8D3D5',
  padding: '8px',
  minHeight: '120px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
};

const ProductFilterSection = ({section}: {section: PageSection}) => {
  const items = section.sectionItems || [];

  const priceFilters = items.filter(
    item =>
      Array.isArray(item.metadata?.filter) &&
      item.metadata.filter.some(f => f.field === 'price'),
  );

  const discountFilters = items.filter(
    item =>
      Array.isArray(item.metadata?.filter) &&
      item.metadata.filter.some(f => f.field === 'discountPercent'),
  );

  const renderPriceCard = (item: SectionItem) => {
    const title = item.title.toLowerCase();
    let prefix = '';
    let amount = '';

    if (title.includes('under')) {
      const idx = title.indexOf('under');
      prefix = item.title.slice(0, idx + 5).trim() || 'Price under';
      amount = item.title.slice(idx + 5).trim();
    } else if (title.includes('above') || title.includes('more than')) {
      const idx =
        title.indexOf('above') >= 0
          ? title.indexOf('above')
          : title.indexOf('more than');
      prefix = item.title.slice(0, idx).trim() + ' above';
      amount = item.title
        .slice(idx)
        .replace(/(above|more than)/i, '')
        .trim();
    } else {
      prefix = 'Price above';
      amount = item.title;
    }

    return (
      <Box
        key={item.id}
        sx={CARD_STYLE}
        onClick={() => window.open(item.metadata?.redirectUrl, '_blank')}
      >
        <Box
          component="img"
          src={item.previewUrl}
          alt={item.title}
          sx={{
            width: '100%',
            height: 140,
            objectFit: 'contain',
            backgroundColor: '#f5f5f5',
            display: 'block',
          }}
        />
        <Box sx={PRICE_TITLE_STYLE}>
          <Typography variant="body1">{prefix}</Typography>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              lineHeight: 1.2,
              fontSize: '1.8rem',
              color: '#00004F',
            }}
          >
            {'₹' + amount}
          </Typography>
        </Box>
      </Box>
    );
  };

  const renderDiscountCard = (item: SectionItem) => {
    const discountParts = item.title.split(/(\d+% off)/).filter(Boolean);
    const mainText = discountParts[0].trim();
    const percentageText = discountParts[1] || item.title;

    return (
      <Box
        key={item.id}
        sx={{
          ...CARD_STYLE,
          display: 'flex',
          flexDirection: 'column-reverse',
        }}
        onClick={() => window.open(item.metadata?.redirectUrl, '_blank')}
      >
        <Box
          component="img"
          src={item.previewUrl}
          alt={item.title}
          sx={{
            width: '100%',
            height: 140,
            objectFit: 'contain',
            backgroundColor: '#f5f5f5',
            display: 'block',
          }}
        />
        <Box sx={DISCOUNT_TITLE_STYLE}>
          <Typography variant="body1" textAlign={'left'} ml={1.5}>
            {mainText}
          </Typography>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              lineHeight: 1.2,
              fontSize: '1.8rem',
              color: '#00004F',
            }}
          >
            {percentageText}
          </Typography>
        </Box>
      </Box>
    );
  };

  return (
    <Stack direction="row" spacing={5}>
      {/* Price Filters */}
      <Box flex={1}>
        <Typography variant="h4" gutterBottom>
          Price Categories
        </Typography>
        <Stack direction="row" flexWrap="wrap" gap={4}>
          {priceFilters.map(renderPriceCard)}
        </Stack>
      </Box>

      {/* Discount Filters */}
      <Box flex={1}>
        <Typography variant="h4" gutterBottom>
          Discount Offers
        </Typography>
        <Stack direction="row" flexWrap="wrap" gap={4}>
          {discountFilters.map(renderDiscountCard)}
        </Stack>
      </Box>
    </Stack>
  );
};

export default ProductFilterSection;
