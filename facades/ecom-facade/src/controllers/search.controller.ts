import {PermissionKeys} from '@local/core';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {FilterGroup, ProductVariant} from '../models';
import {get, getModelSchemaRef, param} from '@loopback/openapi-v3';
import {service} from '@loopback/core';
import {SearchService} from '../services';
import {Filter} from '@loopback/repository';

const basePath = 'search';
export class SearchController {
  constructor(
    @service(SearchService)
    private readonly searchService: SearchService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/suggestions`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Asset model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                partial: true,
              }),
            },
          },
        },
      },
    },
  })
  async getSuggestions(
    @param.query.string('keyword') keyword: string,
  ): Promise<Partial<ProductVariant[]>> {
    return this.searchService.searchSuggestions(keyword);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Search model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                partial: true,
              }),
            },
          },
        },
      },
    },
  })
  async search(
    @param.filter(ProductVariant)
    filter: Filter<ProductVariant>,
    @param.query.string('keyword') keyword?: string,
    @param.query.string('facetValueIds') facetValueIdsStr?: string,
    @param.query.string('collectionIds') collectionIdsStr?: string,
    @param.query.string('priceRange') priceRangeStr?: string,
    @param.query.string('thresholdType') thresholdType?: 'price' | 'discount',
    @param.query.number('thresholdValue') thresholdValue?: number,
    @param.query.string('thresholdOperator')
    thresholdOperator?: 'above' | 'below',
  ): Promise<Partial<ProductVariant[]>> {
    const facetValueIds = facetValueIdsStr?.split(',').filter(Boolean);
    const collectionIds = collectionIdsStr?.split(',').filter(Boolean);
    const priceRange = priceRangeStr?.split(',').filter(Boolean).map(Number);

    return this.searchService.search({
      keyword,
      facetValueIds,
      collectionIds,
      priceRange,
      filter,
      thresholdType,
      thresholdValue,
      thresholdOperator,
    });
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/filters`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Filter model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(FilterGroup),
            },
          },
        },
      },
    },
  })
  async getFilters(
    @param.query.string('keyword') keyword?: string,
    @param.query.string('facetValueIds') facetValueIds?: string,
    @param.query.string('collectionIds') collectionIds?: string,
    @param.query.string('thresholdType') thresholdType?: 'price' | 'discount',
    @param.query.number('thresholdValue') thresholdValue?: number,
    @param.query.string('thresholdOperator')
    thresholdOperator?: 'above' | 'below',
  ): Promise<FilterGroup[]> {
    return this.searchService.getFilters(
      keyword,
      facetValueIds,
      collectionIds,
      thresholdType,
      thresholdValue,
      thresholdOperator,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/quick-filter`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Asset model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(FilterGroup),
          },
        },
      },
    },
  })
  async quickFilter(
    @param.query.string('productVariantIds') productVariantIds: string,
  ): Promise<FilterGroup> {
    return this.searchService.quickFilter(productVariantIds);
  }
}
